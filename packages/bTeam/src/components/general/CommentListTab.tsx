import React from "react";
import { StyleSheet, View } from "react-native";

// Components
import {
  Theme,
  useThemeAwareObject,
  MessageCommentList,
  SPACING,
  TabTitle,
} from "b-ui-lib";
import EmptyTabBody from "./EmptyTabBody";
import CommentListTabSkeleton from "./CommentListTabSkeleton";
import MessageBottomButtons from "./MessageBottomButtons";

// Types
import { MessageComment } from "../../types/message";
import { RelatedUsersList } from "../../types/userMails";

type newCommentPayload = {
  CMM_CMM_Guid?: string;
  notifyUsers: string[];
  description: string;
  restricted: 0 | 1;
};

type Props = {
  comments: MessageComment[];
  recipientsEmails: RelatedUsersList;
  notifiedUsers?: RelatedUsersList;
  handleStarComment: () => void;
  handlePostNewComment: ({
    CMM_CMM_Guid,
    notifyUsers,
    description,
    restricted,
  }: newCommentPayload) => void;
  postMessageCommentLoading: boolean;
  postMessageCommentError: string;
  postMessageCommentSuccess: boolean;
  handlePostMessageCommentSuccessDismiss: () => void;
  handleAddAttachment: () => void;
  uploadAttachmentLoading: boolean;
  uploadAttachmentError: string;
  attachmentsLength: number;
  attachments: any;
  handleDownloadAttachment: () => void;
  handleDownloadAllAttachments: () => void;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  onPressNewComment?: () => void;
  onPressReply: (commentId: string) => void;
  width?: number;
  isLoading?: boolean;
};

const CommentListTab: React.FC<Props> = ({
  comments,
  recipientsEmails,
  notifiedUsers,
  handleStarComment,
  handlePostNewComment,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  attachmentsLength,
  attachments,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  onPressNewComment,
  onPressReply,
  width = 400,
  isLoading = false,
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  // Create buttons array for "Add new comment"
  const BUTTONS = onPressNewComment ? [
    {
      title: "Add new comment",
      isDisabled: false,
      onPress: onPressNewComment,
    },
  ] : [];

  // Show skeleton while loading
  if (isLoading) {
    return <CommentListTabSkeleton width={width} />;
  }

  return (
    <View style={styles.container}>
      <View style={{ paddingHorizontal: SPACING.M, flex: 1 }}>
        <TabTitle title="Comments" count={comments?.length?.toString()} />

        {comments && comments?.length > 0 ? (
          <MessageCommentList
            comments={comments}
            attachments={attachments}
            handlePressReply={onPressReply}
            handlePressStar={handleStarComment}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            fetchNotifiedUsers={fetchNotifiedUsers}
            fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
            fetchNotifiedUsersError={fetchNotifiedUsersError}
            participantsList={notifiedUsers || []}
          />
        ) : (
          <EmptyTabBody iconName="message" emptyMessage="No Comments yet" />
        )}
      </View>

      {BUTTONS.length > 0 && <MessageBottomButtons buttons={BUTTONS} />}
    </View>
  );
};

export default React.memo(CommentListTab);

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      paddingTop: SPACING.M,
      gap: SPACING.SIX,
    },
  });

  return { styles, color };
};
