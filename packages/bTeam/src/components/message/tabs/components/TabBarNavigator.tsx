import React, { useState, useMemo, useCallback } from "react";
import { Pressable, StyleSheet, View } from "react-native";
import { RelatedUsersList } from "../../../../types/userMails";

// Components
import { Theme, useThemeAwareObject, TabBarLabel } from "b-ui-lib";
import HtmlTab from "../HtmlTab/HtmlTab";
import FolderTab from "../FolderTab/FolderTab";
import CaseTab from "../CaseTab/CaseTab";
import CommentListTab from "../../../general/CommentListTab";
import AttachmentTab from "../../../general/AttachmentTab";
import { TEST_IDS } from "../../../../constants/testIds";
// Note: Removed propMemoization imports to fix hook count mismatch

type Props = {
  width: number;
  replies: any;
  comments: any;
  messageAttachmentsIds: any;
  attachments: any;
  attachmentsCount: number;
  downloadedAttachments: number;
  foldersIds: [];
  messageFolders: any;
  caseMetadata: any;
  cases: any;
  recipientsEmails: RelatedUsersList;
  notifiedUsers: RelatedUsersList;
  html: string;
  getMessageBodyLoading: boolean;
  getMessageCommentsLoading: boolean;
  getMessageActionsLoading: boolean;
  getMessageCommentsError: string;
  getMessageActionsError: string;
  messageBodyError: string;
  copyOrMoveMessageFolderError: string;
  moveToFolder: () => void;
  copyToFolder: () => void;
  getMessageActions: () => void;
  handleRetryMessageBody: () => void;
  setDownloadedAttachments: (payload) => void;
  clearDownloadedAttachments: () => void;
  handleClearCopyOrMoveMessageFolderError: () => void;
  postNewComment: () => void;
  postMessageCommentLoading: boolean;
  postMessageCommentError: string;
  postMessageCommentSuccess: boolean;
  handlePostMessageCommentSuccessDismiss: () => void;
  handleStarComment: () => void;
  handleAddAttachment: () => void;
  uploadAttachmentLoading: boolean;
  uploadAttachmentError: string;
  attachmentsLength: number;
  handleDownloadAttachment: () => void;
  handleDownloadAllAttachments: () => void;
  isAnyFileDownloadLoading: boolean;
  isDraft: boolean;
  handleEditDraftMessage: () => void;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  onPressReply: () => void;
  onPressReplyAll: () => void;
  onPressForward: () => void;
  onPressNewComment: () => void;
  onPressCommentReply: (commentId: string) => void;
};

// Note: Removed TabContentSkeleton to fix hook count mismatch

// Custom tab bar component
function CustomTabBar({ activeTab, onTabPress, tabs, color }) {
  return (
    <View style={{ flexDirection: "row", backgroundColor: "#61616B" }}>
      {tabs.map((tab, index) => {
        const isFocused = activeTab === index;

        return (
          <Pressable
            key={tab.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            testID={tab.testID}
            onPress={() => onTabPress(index)}
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              height: 75,
              backgroundColor: isFocused
                ? color.MESSAGE_ITEM__BACKGROUND
                : color.BORDER_EMAIL_FOLDER,
              borderWidth: 1,
              borderRightColor: color.BORDER_EMAIL_FOLDER,
              borderLeftColor: color.BORDER_EMAIL_FOLDER,
              borderBottomColor: color.MESSAGE_ITEM__BACKGROUND,
              borderTopWidth: 3,
              borderTopColor: isFocused
                ? color.MESSAGE_FLAG
                : color.BORDER_EMAIL_FOLDER,
              elevation: 0,
              shadowOpacity: 0,
            }}
          >
            {tab.label({
              focused: isFocused,
              onPress: () => onTabPress(index),
            })}
          </Pressable>
        );
      })}
    </View>
  );
}

const TabBarNavigator: React.FC<Props> = ({
  width,
  replies,
  comments,
  messageAttachmentsIds,
  attachments,
  attachmentsCount,
  downloadedAttachments,
  foldersIds,
  messageFolders,
  caseMetadata,
  cases,
  recipientsEmails,
  html,
  getMessageBodyLoading,
  getMessageCommentsLoading,
  getMessageActionsLoading,
  getMessageCommentsError,
  getMessageActionsError,
  messageBodyError,
  copyOrMoveMessageFolderError,
  moveToFolder,
  copyToFolder,
  getMessageActions,
  handleRetryMessageBody,
  setDownloadedAttachments,
  clearDownloadedAttachments,
  handleClearCopyOrMoveMessageFolderError,
  postNewComment,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleStarComment,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  attachmentsLength,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  isAnyFileDownloadLoading,
  isDraft,
  handleEditDraftMessage,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  notifiedUsers,
  onPressReply,
  onPressReplyAll,
  onPressForward,
  onPressNewComment,
  onPressCommentReply,
  activeTab,
  setActiveTab,
}: Props) => {
  const { color } = useThemeAwareObject(createStyles);

  // Note: Removed unused memoized tab props to fix hook count mismatch

  // Define tabs configuration - simplified without lazy loading to prevent hook count mismatch
  const tabs = useMemo(
    () => [
      {
        key: "html-body",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel iconName="mail" focused={focused} onPress={onPress} />
        ),
        component: () => (
          <HtmlTab
            html={html}
            width={width}
            replies={replies}
            getMessageBodyLoading={getMessageBodyLoading}
            getMessageActionsLoading={getMessageActionsLoading}
            handleRefreshList={getMessageActions}
            getMessageActionsError={getMessageActionsError}
            messageBodyError={messageBodyError}
            handleRetryMessageBody={handleRetryMessageBody}
            isDraft={isDraft}
            handleEditDraftMessage={handleEditDraftMessage}
            onPressReply={onPressReply}
            onPressReplyAll={onPressReplyAll}
            onPressForward={onPressForward}
          />
        ),
      },
      {
        key: "comments-list",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel
            iconName="message"
            focused={focused}
            count={comments?.length}
            onPress={onPress}
          />
        ),
        component: () => (
          <CommentListTab
            comments={comments}
            recipientsEmails={recipientsEmails}
            handlePostNewComment={postNewComment}
            handleStarComment={handleStarComment}
            postMessageCommentLoading={postMessageCommentLoading}
            postMessageCommentError={postMessageCommentError}
            postMessageCommentSuccess={postMessageCommentSuccess}
            handlePostMessageCommentSuccessDismiss={
              handlePostMessageCommentSuccessDismiss
            }
            handleAddAttachment={handleAddAttachment}
            uploadAttachmentLoading={uploadAttachmentLoading}
            uploadAttachmentError={uploadAttachmentError}
            attachmentsLength={attachmentsLength}
            attachments={attachments}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            notifiedUsers={notifiedUsers}
            fetchNotifiedUsers={fetchNotifiedUsers}
            fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
            fetchNotifiedUsersError={fetchNotifiedUsersError}
            onPressNewComment={onPressNewComment}
            onPressReply={onPressCommentReply}
            width={width}
            isLoading={getMessageCommentsLoading}
          />
        ),
      },
      {
        key: "attachment-list",
        testID: TEST_IDS.messageAttachmentTabTabBarIcon,
        label: ({ focused, onPress }) => (
          <TabBarLabel
            testID={TEST_IDS.messageAttachmentTabTabBarIcon}
            iconName="paperclip"
            focused={focused}
            count={attachmentsCount?.toString()}
            onPress={onPress}
          />
        ),
        component: () => (
          <AttachmentTab
            attachmentsIds={messageAttachmentsIds}
            messageAttachments={attachments}
            attachmentsCount={attachmentsCount}
            downloadedAttachments={downloadedAttachments}
            handleRefreshList={getMessageActions}
            getMessageActionsError={getMessageActionsError}
            getMessageActionsLoading={getMessageActionsLoading}
            clearDownloadedAttachments={clearDownloadedAttachments}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            isAnyFileDownloadLoading={isAnyFileDownloadLoading}
          />
        ),
      },
      {
        key: "folder-list",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel
            iconName="folder"
            focused={focused}
            count={foldersIds?.length}
            onPress={onPress}
          />
        ),
        component: () => (
          <FolderTab
            foldersIds={foldersIds}
            messageFolders={messageFolders}
            moveToFolder={moveToFolder}
            copyToFolder={copyToFolder}
            isLoading={getMessageActionsLoading}
            handleRefreshList={getMessageActions}
            copyOrMoveMessageFolderError={copyOrMoveMessageFolderError}
            getMessageActionsError={getMessageActionsError}
            handleClearCopyOrMoveMessageFolderError={
              handleClearCopyOrMoveMessageFolderError
            }
            width={width}
          />
        ),
      },
      {
        key: "cases-list",
        testID: undefined,
        label: ({ focused, onPress }) => (
          <TabBarLabel iconName="info" focused={focused} onPress={onPress} />
        ),
        component: () => (
          <CaseTab
            caseMetadata={caseMetadata}
            cases={cases}
            width={width}
            isLoading={getMessageActionsLoading}
          />
        ),
      },
    ],
    [
      html,
      width,
      replies,
      getMessageBodyLoading,
      getMessageActionsLoading,
      getMessageActions,
      getMessageActionsError,
      messageBodyError,
      handleRetryMessageBody,
      isDraft,
      handleEditDraftMessage,
      onPressReply,
      onPressReplyAll,
      onPressForward,
      comments,
      recipientsEmails,
      postNewComment,
      handleStarComment,
      postMessageCommentLoading,
      postMessageCommentError,
      postMessageCommentSuccess,
      handlePostMessageCommentSuccessDismiss,
      handleAddAttachment,
      uploadAttachmentLoading,
      uploadAttachmentError,
      attachmentsLength,
      attachments,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      notifiedUsers,
      fetchNotifiedUsers,
      fetchNotifiedUsersLoading,
      fetchNotifiedUsersError,
      messageAttachmentsIds,
      attachmentsCount,
      downloadedAttachments,
      clearDownloadedAttachments,
      isAnyFileDownloadLoading,
      foldersIds,
      messageFolders,
      moveToFolder,
      copyToFolder,
      copyOrMoveMessageFolderError,
      handleClearCopyOrMoveMessageFolderError,
      caseMetadata,
      cases,
    ]
  );

  // Handle tab press - simplified without lazy loading
  const handleTabPress = useCallback(
    (tabIndex: number) => {
      if (tabIndex === activeTab) return;
      setActiveTab(tabIndex);
    },
    [activeTab]
  );

  // Render tab content - always render the actual component to maintain consistent hook count
  const renderTabContent = useCallback(() => {
    return tabs[activeTab].component();
  }, [activeTab, tabs]);

  return (
    <View style={{ flex: 1 }}>
      <CustomTabBar
        activeTab={activeTab}
        onTabPress={handleTabPress}
        tabs={tabs}
        color={color}
      />
      <View style={{ flex: 1 }}>{renderTabContent()}</View>
    </View>
  );
};

const MemoizedTabBarNavigator = React.memo(TabBarNavigator);
export default MemoizedTabBarNavigator;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
  });

  return { styles, color };
};
