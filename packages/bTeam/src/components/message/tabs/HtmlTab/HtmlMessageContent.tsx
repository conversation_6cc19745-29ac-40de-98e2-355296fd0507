import React, { useMemo } from "react";
import { <PERSON>rollView, StyleSheet, View, Platform } from "react-native";
import RenderHtml from "react-native-render-html";

// Components
import { FONT_SIZES, ICON_POSITIONS, IconTextButton, SPACING } from "b-ui-lib";
import TabErrorMessage from "../../TabErrorMessage";

type Props = {
  html: string;
  width: number;
  messageBodyError: string;
  handleRetryMessageBody: () => void;
  onHorizontalScrollStart?: () => void;
  onHorizontalScrollEnd?: () => void;
};

const HtmlMessageContent = ({
  html,
  width,
  messageBodyError,
  handleRetryMessageBody,
  onHorizontalScrollStart,
  onHorizontalScrollEnd,
}: Props) => {
  // Detect if content likely needs horizontal scrolling
  const needsHorizontalScroll = useMemo(() => {
    if (!html) return false;

    // Check for common indicators of wide content
    const hasTable = /<table/i.test(html);
    const hasPreCode = /<pre|<code/i.test(html);
    const hasLongLines = html.split("\n").some((line) => line.length > 100);
    const hasImages = /<img/i.test(html);
    const hasWideImages = /<img[^>]*width\s*=\s*["']?(\d+)/i.test(html);

    // For Android, be more aggressive about detecting wide content
    if (Platform.OS === 'android') {
      return hasTable || hasPreCode || hasLongLines || hasImages || hasWideImages;
    }

    return hasTable || hasPreCode || hasLongLines || hasWideImages;
  }, [html]);

  const contentWidth = needsHorizontalScroll ? Math.max(width, 1000) : width;

  if (messageBodyError) {
    return (
      <View style={styles.errorContainer}>
        <TabErrorMessage
          text={messageBodyError}
          isVisible={Boolean(messageBodyError)}
        />

        <IconTextButton
          iconPosition={ICON_POSITIONS.left}
          iconName="Redo"
          iconSize={20}
          iconColor="#007AFF"
          title="Retry"
          onPress={handleRetryMessageBody}
          textStyle={styles.retryButtonText}
          containerStyle={styles.retryButton}
        />
      </View>
    );
  }

  // Platform-specific scrolling approach
  if (Platform.OS === "android" && needsHorizontalScroll) {
    // Android with wide content: Use a simple approach that works with the parent FlatList
    // The key is to use scrollEnabled={false} on the outer container when horizontal scrolling is needed
    return (
      <View style={styles.scrollContainer}>
        <View style={styles.androidWideContentContainer}>
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={true}
            showsVerticalScrollIndicator={false}
            scrollEnabled={true}
            bounces={false}
            directionalLockEnabled={false} // Allow both directions
            style={styles.androidHorizontalScroll}
            contentContainerStyle={{
              minWidth: Math.max(width, contentWidth),
              paddingRight: 50,
            }}
            // Key properties to prevent parent gesture conflicts
            disableIntervalMomentum={true}
            decelerationRate="fast"
            onScrollBeginDrag={() => {
              onHorizontalScrollStart?.();
            }}
            onScrollEndDrag={() => {
              onHorizontalScrollEnd?.();
            }}
          >
            <View style={{ width: Math.max(width, contentWidth) }}>
              <ScrollView
                showsVerticalScrollIndicator={false}
                scrollEnabled={true}
                directionalLockEnabled={false} // Allow both directions for better gesture handling
                style={{ flex: 1 }}
                contentContainerStyle={{ flexGrow: 1 }}
                nestedScrollEnabled={true}
              >
                <RenderHtml
                  contentWidth={Math.max(width, contentWidth)}
                  source={{ html }}
                  enableExperimentalMarginCollapsing={true}
                  computeEmbeddedMaxWidth={(contentWidth) => Math.max(width, contentWidth, 1000)}
                  ignoredDomTags={["map", "area"]}
                  renderersProps={{
                    img: {
                      enableExperimentalPercentWidth: false, // Disable for Android
                    },
                    blockquote: {
                      htmlAttribs: {
                        style:
                          "margin: 16px 0; padding: 16px; border-left: 4px solid #ddd; background: #f9f9f9;",
                      },
                    },
                  }}
                  defaultTextProps={{
                    style: styles.htmlTextStyle,
                  }}
                  tagsStyles={{
                    body: {
                      fontSize: FONT_SIZES.FOURTEEN,
                      lineHeight: 20,
                    },
                    p: {
                      marginBottom: 12,
                    },
                    div: {
                      marginBottom: 8,
                    },
                    img: {
                      maxWidth: 'none', // Allow images to be their natural width
                    },
                  }}
                  systemFonts={["System"]}
                />
              </ScrollView>
            </View>
          </ScrollView>
        </View>
      </View>
    );
  }

  // iOS or normal content: Use nested ScrollViews
  return (
    <ScrollView
      style={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      horizontal={false}
      nestedScrollEnabled={true}
    >
      {needsHorizontalScroll ? (
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ minWidth: width }}
          nestedScrollEnabled={true}
        >
          <RenderHtml
            contentWidth={contentWidth}
            source={{ html }}
            enableExperimentalMarginCollapsing={true}
            computeEmbeddedMaxWidth={(contentWidth) =>
              Math.max(contentWidth, 800)
            }
            ignoredDomTags={["map", "area"]}
            renderersProps={{
              img: {
                enableExperimentalPercentWidth: true,
              },
              blockquote: {
                htmlAttribs: {
                  style:
                    "margin: 16px 0; padding: 16px; border-left: 4px solid #ddd; background: #f9f9f9;",
                },
              },
            }}
            defaultTextProps={{
              style: styles.htmlTextStyle,
            }}
            tagsStyles={{
              body: {
                fontSize: FONT_SIZES.FOURTEEN,
                lineHeight: 20,
              },
              p: {
                marginBottom: 12,
              },
              div: {
                marginBottom: 8,
              },
            }}
            systemFonts={["System"]}
          />
        </ScrollView>
      ) : (
        <RenderHtml
          contentWidth={contentWidth}
          source={{ html }}
          enableExperimentalMarginCollapsing={true}
          computeEmbeddedMaxWidth={(contentWidth) => contentWidth}
          ignoredDomTags={["map", "area"]}
          renderersProps={{
            img: {
              enableExperimentalPercentWidth: true,
            },
            blockquote: {
              htmlAttribs: {
                style:
                  "margin: 16px 0; padding: 16px; border-left: 4px solid #ddd; background: #f9f9f9;",
              },
            },
          }}
          defaultTextProps={{
            style: styles.htmlTextStyle,
          }}
          tagsStyles={{
            body: {
              fontSize: FONT_SIZES.FOURTEEN,
              lineHeight: 20,
            },
            p: {
              marginBottom: 12,
            },
            div: {
              marginBottom: 8,
            },
          }}
          systemFonts={["System"]}
        />
      )}
    </ScrollView>
  );
};

// HtmlMessageContent.displayName = 'HtmlMessageContent';

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  htmlTextStyle: {
    color: "#000000",
    fontSize: FONT_SIZES.FOURTEEN,
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: SPACING.L,
  },
  retryButton: {
    marginTop: SPACING.M,
    paddingHorizontal: SPACING.L,
    paddingVertical: SPACING.S,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#007AFF",
  },
  retryButtonText: {
    fontSize: FONT_SIZES.FOURTEEN,
    fontWeight: "600",
    color: "#007AFF",
  },
  // Android-specific styles
  androidScrollContent: {
    flexGrow: 1,
  },
  androidHorizontalScroll: {
    flex: 1,
  },
  androidOuterScroll: {
    flex: 1,
    height: '100%',
  },
  androidHorizontalContent: {
    minWidth: '100%',
    flexGrow: 1,
  },
  androidInnerScroll: {
    width: 800, // Fixed width for the inner scroll
    minHeight: '100%',
  },
  androidWideContentContainer: {
    flex: 1,
    minHeight: 400, // Ensure minimum height
  },
  androidHorizontalScrollOnly: {
    flexGrow: 0,
    height: 'auto',
  },
});

export default React.memo(HtmlMessageContent);
