import React, { useEffect, useMemo, useCallback, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, useWindowDimensions } from "react-native";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
  useFocusEffect,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import {
  clearCopyOrMoveMessageFolderError,
  flagMessages,
  getMessageActions,
  getMessageBody,
  getMessageComments,
  getMessageMetadata,
  performDeleteAction,
  performMarkAsReadUnreadAction,
  performUnDeleteAction,
} from "../slices/gridMessageSlice";
import {
  clearDownloadedAttachments,
  setDownloadedAttachments,
} from "../slices/attachmentsSlice";
import { sendMessageClearStatus } from "../slices/generalSlice";
import { Message } from "../types/message";
import { MessageActionTypes } from "../types/MessageActionTypes";

// Helpers
import {
  downloadMultipleAttachments,
  downloadSingleAttachment,
} from "../../../bcomponents/downloadHelpers/downloadAttachmentHelper";
import { calculateAvatarName } from "../helpers/calculateAvatarName";

// Components
import CommentsHOC from "./CommentsHOC";
import MessageScreen from "../components/message/MessageScreen";

// Constants
import { EMAIL_CASES } from "../constants/emailCases";
import { SCREEN_NAMES } from "../constants/screenNames";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";
import { FLAGGED_API_VALUES } from "../constants/apiValues";
import { FOLDER_NAMES } from "../constants/folderNames";

type Props = {
  UMS_Guid: string;
};

type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

const MessageHOC: React.FC<Props> = ({ UMS_Guid }: Props) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const { token } = useSelector((state: any) => state.persist?.bTeamAuth || {});

  // Selective Redux subscriptions - only subscribe to specific data slices needed
  const message: Message = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.gridMessages?.byId?.[UMS_Guid]
  );

  const casesList = useSelector(
    (state: any) => state.persist?.bTeamCasesSlice?.casesList || {}
  );

  const attachments = useSelector(
    (state: any) => state.root?.bTeamAttachmentsSlice?.attachments
  );
  const downloadedAttachments = useSelector(
    (state: any) => state.root?.bTeamAttachmentsSlice?.downloadedAttachments || {}
  );

  // Selective subscription to only the loading states we need
  const getMessageBodyLoading = useSelector(
    (state: any) =>
      state.persist?.gridMessageSlice?.getMessageBodyLoading?.[UMS_Guid] || false
  );
  const getMessageCommentsLoading = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.getMessageCommentsLoading || false
  );
  const getMessageActionsLoading = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.getMessageActionsLoading || false
  );

  // Selective subscription to only the error states we need
  const getMessageBodyError = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.getMessageBodyError
  );
  const getMessageCommentsError = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.getMessageCommentsError
  );
  const getMessageActionsError = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.getMessageActionsError
  );
  const copyOrMoveMessageFolderError = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.copyOrMoveMessageFolderError
  );

  // Selective subscription to folder data
  const folders = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.folders || { allIds: [], byId: {} }
  );
  const selectedFolderId = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.selectedFolderId
  );
  const messageFolders = useSelector(
    (state: any) => state.persist?.gridMessageSlice?.messageFolders
  );

  const sendDraftMessageSuccess = useSelector(
    (state: any) => state.root?.bTeamGeneralSlice?.sendDraftMessageSuccess
  );
  const isDeletedFolder =
    folders?.allIds?.find(
      (id) => folders?.byId?.[id]?.name === FOLDER_NAMES.deletedItems
    ) === selectedFolderId;

  const cases = useMemo(() => {
    return (
      message?.caseIds
        ?.map((id) => {
          const item = casesList?.byId?.[id];

          return item
            ? {
                id: item.CAS_Guid,
                name: `${item.CAS_Reference} // ${item.CAS_Title}`,
                date: item.CAS_UpdatedTimestamp,
              }
            : null;
        })
        .filter(Boolean) || []
    );
  }, [message?.caseIds, casesList?.byId]);

  // Memoized stable callbacks to prevent child re-renders
  const handleDeleteMessage = useCallback(() => {
    dispatch(performDeleteAction({ ids: [UMS_Guid] }));
    navigation.goBack();
  }, [dispatch, UMS_Guid, navigation]);

  const handleUnDeleteMessage = useCallback(() => {
    dispatch(performUnDeleteAction({ ids: [UMS_Guid] }));
    navigation.goBack();
  }, [dispatch, UMS_Guid, navigation]);

  const handleFlagMessageAction = useCallback(() => {
    dispatch(
      flagMessages({
        ids: [UMS_Guid],
        value: message?.isFlagged
          ? FLAGGED_API_VALUES.unFlag
          : FLAGGED_API_VALUES.flag,
      })
    );

    navigation.setOptions({
      isFlagged: !message?.isFlagged,
    });
  }, [dispatch, UMS_Guid, message?.isFlagged]);

  const handleMarkAsReadUnreadAction = useCallback(() => {
    // If message is viewed (read), mark as unread (mode 7)
    // If message is not viewed (unread), mark as read (mode 6)
    const mode = message?.isViewed ? 7 : 6;
    dispatch(performMarkAsReadUnreadAction({ ids: [UMS_Guid], mode }));

    navigation.setOptions({
      isViewed: !message?.isViewed,
    });
  }, [dispatch, UMS_Guid, message?.isViewed]);

  const handleGetMessageActions = useCallback(
    () => dispatch(getMessageActions({ UMS_Guid: UMS_Guid })),
    [dispatch, UMS_Guid]
  );

  const setDownloadedAttachmentsAction = useCallback(
    (payload) => {
      dispatch(setDownloadedAttachments(payload));
    },
    [dispatch]
  );

  const clearDownloadedAttachmentsAction = useCallback(() => {
    dispatch(clearDownloadedAttachments());
  }, [dispatch]);

  const clearCopyOrMoveMessageFolderErrorAction = useCallback(() => {
    dispatch(clearCopyOrMoveMessageFolderError());
  }, [dispatch]);

  const handleMoveSelectedEmailsToFolder = useCallback(() => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: false,
      messageId: UMS_Guid,
    });
  }, [navigation, UMS_Guid]);

  const handleCopySelectedEmailsToFolder = useCallback(() => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: false,
      messageId: UMS_Guid,
    });
  }, [navigation, UMS_Guid]);

  useEffect(() => {
    const subscription = BackHandler.addEventListener("hardwareBackPress", () => {
      navigation.goBack();
      return true;
    });

    // Cleanup debounce timer on unmount
    return () => {
      if (subscription && typeof subscription.remove === "function") {
        subscription.remove();
      }
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      deleteMessage: isDeletedFolder
        ? (handleUnDeleteMessage || (() => {}))
        : (handleDeleteMessage || (() => {})),
      handleFlagMessage: handleFlagMessageAction || (() => {}),
      handleMarkAsReadUnread: handleMarkAsReadUnreadAction || (() => {}),
    });
  }, [navigation, message, isDeletedFolder]);

  // Initial load for the message (only on component mount)
  useEffect(() => {
    if (!message?.fullMessageBody || message?.inOut === 3) {
      dispatch(getMessageBody({ UMS_Guid }));
    }

    dispatch(getMessageComments({ guid: UMS_Guid, entityId: 1001 }));
    dispatch(getMessageMetadata({ guid: UMS_Guid }));
    dispatch(getMessageActions({ UMS_Guid }));
  }, [UMS_Guid]); // Run when UMS_Guid changes

  useFocusEffect(
    useCallback(() => {
      if (sendDraftMessageSuccess) dispatch(sendMessageClearStatus());
    }, [sendDraftMessageSuccess])
  );

  const repliesData = useMemo(
    () =>
      message?.repliedByUser
        ? [
            {
              id: 1,
              avatarName: calculateAvatarName(message?.repliedByUser),
              from: "",
              username: message?.repliedByUser,
              date: message?.timeReplied,
            },
          ]
        : [],
    [message?.repliedByUser, message?.timeReplied]
  );

  const handleFilesDownloadStatus = useCallback(
    (attachmentId: string, downloadStatus: FileDownloadStatus) => {
      setDownloadedAttachmentsAction({
        id: attachmentId,
        status: downloadStatus,
        isLoading: downloadStatus === FILE_DOWNLOAD_STATUS.loading,
      });
    },
    [setDownloadedAttachmentsAction]
  );

  const handleDownloadAttachment = useCallback(
    async (attachmentId: string) => {
      clearDownloadedAttachmentsAction();

      await downloadSingleAttachment(
        token,
        attachmentId,
        attachments?.byId?.[attachmentId]?.name,
        handleFilesDownloadStatus
      );
    },
    [
      token,
      attachments?.byId,
      clearDownloadedAttachmentsAction,
      handleFilesDownloadStatus,
    ]
  );

  const handleDownloadAllAttachments = useCallback(
    async (attachmentIds: string[]) => {
      // Clear any existing downloaded attachments.
      clearDownloadedAttachmentsAction();

      // If there are no attachments, we can optionally return or alert.
      if (attachmentIds.length === 0) {
        console.log("No attachments found");
        return;
      }

      const fileNamesMapping: Record<string, string> = {};
      if (attachments?.byId) {
        Object.keys(attachments.byId).forEach((id) => {
          // Ensure that the object has a "name" property.
          fileNamesMapping[id] = attachments.byId[id].name;
        });
      }

      // Download all attachments.
      await downloadMultipleAttachments(
        token,
        attachmentIds,
        fileNamesMapping,
        handleFilesDownloadStatus
      );
    },
    [
      token,
      attachments?.byId,
      clearDownloadedAttachmentsAction,
      handleFilesDownloadStatus,
    ]
  );

  const isAnyFileDownloadLoading = useMemo(
    () =>
      Object.values(downloadedAttachments || {}).some(
        (status) => status === FILE_DOWNLOAD_STATUS.loading
      ),
    [downloadedAttachments]
  );

  const handleEditDraftMessage = useCallback(() => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: UMS_Guid,
      messageType: MessageActionTypes.DraftEdit,
    });
  }, [navigation, UMS_Guid]);

  const handleOnPressReply = useCallback(() => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: UMS_Guid,
      messageType: MessageActionTypes.Reply,
    });
  }, [navigation, UMS_Guid]);

  const handleOnPressReplyAll = useCallback(() => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: UMS_Guid,
      messageType: MessageActionTypes.ReplyAll,
    });
  }, [navigation, UMS_Guid]);

  const handleOnPressForward = useCallback(() => {
    navigation.navigate(SCREEN_NAMES.composeMessage, {
      UMS_Guid: UMS_Guid,
      messageType: MessageActionTypes.Forward,
    });
  }, [navigation, UMS_Guid]);

  const handleRetryMessageBody = useCallback(() => {
    dispatch(getMessageBody({ UMS_Guid: UMS_Guid }));
  }, [dispatch, UMS_Guid]);

  // Memoized stable handlers object to prevent child re-renders
  const staticHandlers = useMemo(
    () => ({
      moveToFolder: handleMoveSelectedEmailsToFolder,
      copyToFolder: handleCopySelectedEmailsToFolder,
      getMessageActions: handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachments: setDownloadedAttachmentsAction,
      clearDownloadedAttachments: clearDownloadedAttachmentsAction,
      handleClearCopyOrMoveMessageFolderError:
        clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      onPressReply: handleOnPressReply,
      onPressReplyAll: handleOnPressReplyAll,
      onPressForward: handleOnPressForward,
    }),
    [
      handleMoveSelectedEmailsToFolder,
      handleCopySelectedEmailsToFolder,
      handleGetMessageActions,
      handleRetryMessageBody,
      setDownloadedAttachmentsAction,
      clearDownloadedAttachmentsAction,
      clearCopyOrMoveMessageFolderErrorAction,
      handleDownloadAttachment,
      handleDownloadAllAttachments,
      handleEditDraftMessage,
      handleOnPressReply,
      handleOnPressReplyAll,
      handleOnPressForward,
    ]
  );

  // Memoized props object to prevent unnecessary re-renders of MessageScreen
  const commonMessageProps = useMemo(
    () => ({
      width,
      message: message,
      replies: repliesData,
      messageAttachmentsIds: message?.attachmentsIds,
      attachments,
      attachmentsCount: message?.attachmentsCount,
      downloadedAttachments,
      foldersIds: message?.foldersIds,
      messageFolders,
      caseMetadata: message?.messageMetadata,
      cases,
      getMessageBodyLoading,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      messageBodyError: getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      isDraft: message?.inOut === 3,
      ...staticHandlers,
    }),
    [
      width,
      message,
      repliesData,
      attachments,
      downloadedAttachments,
      messageFolders,
      cases,
      getMessageBodyLoading,
      getMessageCommentsLoading,
      getMessageActionsLoading,
      getMessageBodyError,
      getMessageCommentsError,
      getMessageActionsError,
      copyOrMoveMessageFolderError,
      isAnyFileDownloadLoading,
      staticHandlers,
    ]
  );

  return (
    <CommentsHOC
      guid={UMS_Guid}
      entityId="1001"
      umsGuidToSendComment={UMS_Guid}
      commentIds={message?.messageCommentIds}
      refreshListCallback={() =>
        dispatch(
          getMessageComments({
            guid: UMS_Guid,
            entityId: 1001,
          })
        )
      }
    >
      <MessageScreen {...(commonMessageProps as any)} />
    </CommentsHOC>
  );
};

export default React.memo(MessageHOC);
